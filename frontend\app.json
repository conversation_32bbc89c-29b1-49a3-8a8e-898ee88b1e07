{"expo": {"name": "frontend", "slug": "frontend", "version": "1.0.0", "orientation": "portrait", "icon": "./assets/images/icon.png", "scheme": "frontend", "userInterfaceStyle": "automatic", "newArchEnabled": true, "ios": {"supportsTablet": true}, "android": {"adaptiveIcon": {"foregroundImage": "./assets/images/adaptive-icon.png", "backgroundColor": "#ffffff"}, "edgeToEdgeEnabled": true}, "web": {"bundler": "metro", "output": "static", "favicon": "./assets/images/favicon.png"}, "plugins": ["expo-router", ["expo-splash-screen", {"image": "./assets/images/splash-icon.png", "imageWidth": 200, "resizeMode": "contain", "backgroundColor": "#ffffff"}], ["react-native-video", {"androidExtensions": {"useExoplayerHls": true, "useExoplayerDash": true, "useExoplayerSmoothStreaming": true}}]], "experiments": {"typedRoutes": true}}}