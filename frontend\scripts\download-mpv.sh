#!/bin/bash

# <PERSON>ript to download and set up MPV libraries for Android and desktop platforms
# This script downloads prebuilt MPV libraries and sets them up for use in the project

set -e

# Detect platform
PLATFORM="$(uname -s)"
case "${PLATFORM}" in
    Linux*)     PLATFORM=linux;;
    Darwin*)    PLATFORM=macos;;
    MINGW*)     PLATFORM=windows;;
    *)          PLATFORM="unknown"
esac

echo "Detected platform: ${PLATFORM}"

# Create directories
mkdir -p android/app/src/main/jniLibs/armeabi-v7a
mkdir -p android/app/src/main/jniLibs/arm64-v8a
mkdir -p android/app/src/main/jniLibs/x86
mkdir -p android/app/src/main/jniLibs/x86_64
mkdir -p android/app/src/main/cpp/include/mpv

# Download MPV libraries
echo "Downloading MPV libraries..."

# URLs for prebuilt MPV libraries
MPV_ANDROID_URL="https://f-droid.org/repo/is.xyz.mpv_41.apk"
MPV_INCLUDE_URL="https://github.com/mpv-player/mpv/archive/refs/tags/v0.37.0.tar.gz"

# Download and extract MPV Android APK from F-Droid
echo "Downloading MPV Android APK from F-Droid..."
wget -O mpv-android.apk $MPV_ANDROID_URL

# Extract libraries for all architectures
echo "Extracting libraries for all architectures..."
unzip -j mpv-android.apk "lib/arm64-v8a/libmpv.so" -d android/app/src/main/jniLibs/arm64-v8a/
unzip -j mpv-android.apk "lib/armeabi-v7a/libmpv.so" -d android/app/src/main/jniLibs/armeabi-v7a/
unzip -j mpv-android.apk "lib/x86/libmpv.so" -d android/app/src/main/jniLibs/x86/ || echo "No x86 library found, skipping"
unzip -j mpv-android.apk "lib/x86_64/libmpv.so" -d android/app/src/main/jniLibs/x86_64/

# Download and extract MPV headers
wget -O mpv-source.tar.gz $MPV_INCLUDE_URL
tar -xzf mpv-source.tar.gz
cp -r mpv-0.37.0/libmpv/client.h android/app/src/main/cpp/include/mpv/
cp -r mpv-0.37.0/libmpv/render.h android/app/src/main/cpp/include/mpv/
cp -r mpv-0.37.0/libmpv/render_gl.h android/app/src/main/cpp/include/mpv/

# Set up desktop libraries if needed
if [ "$PLATFORM" != "unknown" ]; then
    echo "Setting up MPV for desktop platform: ${PLATFORM}"

    # Create desktop directories
    mkdir -p desktop/mpv

    case "${PLATFORM}" in
        linux)
            echo "For Linux, please install MPV using your package manager:"
            echo "sudo apt install mpv libmpv-dev    # For Debian/Ubuntu"
            echo "sudo dnf install mpv mpv-devel     # For Fedora"
            echo "sudo pacman -S mpv                 # For Arch Linux"
            ;;

        macos)
            echo "For macOS, please install MPV using Homebrew:"
            echo "brew install mpv"
            ;;

        windows)
            echo "Downloading MPV for Windows..."
            # Download Windows MPV
            MPV_WINDOWS_URL="https://sourceforge.net/projects/mpv-player-windows/files/release/mpv-0.37.0-x86_64.7z"
            wget -O mpv-windows.7z "$MPV_WINDOWS_URL"

            # Extract Windows MPV
            7z x mpv-windows.7z -odesktop/mpv

            echo "MPV for Windows set up successfully!"

            # Clean up Windows files
            rm -rf mpv-windows.7z
            ;;
    esac
fi

# Clean up
rm -rf mpv-android.apk mpv-source.tar.gz mpv-0.37.0

echo "MPV libraries and headers set up successfully!"
