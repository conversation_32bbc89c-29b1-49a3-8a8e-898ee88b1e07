cmake_minimum_required(VERSION 3.4.1)

# Set path to prebuilt MPV libraries
set(MPV_LIB_DIR ${CMAKE_SOURCE_DIR}/src/main/jniLibs/${ANDROID_ABI})

# Add MPV JNI library
add_library(mpv-jni SHARED
            mpv_jni.cpp)

# Find the Android log library
find_library(log-lib log)

# Include MPV headers
include_directories(${CMAKE_SOURCE_DIR}/src/main/cpp/include)

# Link against prebuilt MPV library
add_library(mpv SHARED IMPORTED)
set_target_properties(mpv PROPERTIES IMPORTED_LOCATION ${MPV_LIB_DIR}/libmpv.so)

# Link libraries
target_link_libraries(mpv-jni
                      mpv
                      ${log-lib})
