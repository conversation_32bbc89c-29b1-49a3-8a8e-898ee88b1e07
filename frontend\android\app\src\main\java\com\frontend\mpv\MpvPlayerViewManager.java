package com.frontend.mpv;

import android.util.Log;
import android.view.View;
import android.widget.FrameLayout;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;

import com.facebook.react.bridge.ReadableMap;
import com.facebook.react.common.MapBuilder;
import com.facebook.react.uimanager.SimpleViewManager;
import com.facebook.react.uimanager.ThemedReactContext;
import com.facebook.react.uimanager.annotations.ReactProp;
import com.facebook.react.uimanager.events.RCTEventEmitter;

import java.util.Map;

public class MpvPlayerViewManager extends SimpleViewManager<MpvPlayerView> {
    private static final String TAG = "MpvPlayerViewManager";
    public static final String REACT_CLASS = "MpvPlayerView";

    @Override
    @NonNull
    public String getName() {
        return REACT_CLASS;
    }

    @Override
    @NonNull
    protected MpvPlayerView createViewInstance(@NonNull ThemedReactContext reactContext) {
        return new MpvPlayerView(reactContext);
    }

    @ReactProp(name = "source")
    public void setSource(MpvPlayerView view, @Nullable ReadableMap source) {
        if (source != null && source.hasKey("uri")) {
            String uri = source.getString("uri");
            view.setSource(uri);
        }
    }

    @ReactProp(name = "paused", defaultBoolean = false)
    public void setPaused(MpvPlayerView view, boolean paused) {
        view.setPaused(paused);
    }

    @ReactProp(name = "volume", defaultFloat = 1.0f)
    public void setVolume(MpvPlayerView view, float volume) {
        view.setVolume(volume);
    }

    @ReactProp(name = "repeat", defaultBoolean = false)
    public void setRepeat(MpvPlayerView view, boolean repeat) {
        view.setRepeat(repeat);
    }

    @ReactProp(name = "muted", defaultBoolean = false)
    public void setMuted(MpvPlayerView view, boolean muted) {
        view.setMuted(muted);
    }

    @ReactProp(name = "autoplay", defaultBoolean = true)
    public void setAutoplay(MpvPlayerView view, boolean autoplay) {
        view.setAutoplay(autoplay);
    }

    @Override
    public Map<String, Object> getExportedCustomDirectEventTypeConstants() {
        MapBuilder.Builder<String, Object> builder = MapBuilder.builder();
        builder.put("onMpvLoad", MapBuilder.of("registrationName", "onMpvLoad"));
        builder.put("onMpvProgress", MapBuilder.of("registrationName", "onMpvProgress"));
        builder.put("onMpvEnd", MapBuilder.of("registrationName", "onMpvEnd"));
        builder.put("onMpvError", MapBuilder.of("registrationName", "onMpvError"));
        builder.put("onMpvBuffer", MapBuilder.of("registrationName", "onMpvBuffer"));
        builder.put("onMpvPlaying", MapBuilder.of("registrationName", "onMpvPlaying"));
        builder.put("onMpvPaused", MapBuilder.of("registrationName", "onMpvPaused"));
        return builder.build();
    }
}
