// MPV Player TypeScript implementation for React Native
import React, { forwardRef } from "react"
import { NativeModules, requireNativeComponent, Platform, View, Text } from "react-native"
import { MpvPlayerProps, MpvPlayerMethods } from "./types"

// Check if native module is available
const isNativeModuleAvailable = !!NativeModules.MpvPlayer

// Native modules
const MpvModule = NativeModules.MpvPlayer
    ? NativeModules.MpvPlayer
    : new Proxy(
          {},
          {
              get(_target, prop) {
                  // Instead of throwing an error, provide no-op functions
                  if (typeof prop === "string") {
                      console.warn(`MPV native module method '${prop}' not available`)
                      return () => {}
                  }
                  return undefined
              },
          }
      )

// Log warning if native module is not available
if (!isNativeModuleAvailable) {
    console.warn(
        `The MPV player native module is not available. This could be because: \n\n` +
            Platform.select({ ios: "- You haven't run 'pod install'\n", default: "" }) +
            "- The native module is not properly linked\n" +
            "- You are using Expo Go, which doesn't support native modules\n" +
            "- You are running on a platform that doesn't support MPV\n"
    )
}

// Native component
let MpvPlayerView: any
try {
    // Try to get the native component
    MpvPlayerView = requireNativeComponent("MpvPlayerView")
} catch (error) {
    // Fallback to a View component if the native component isn't available
    console.warn("MPV native component not available, using fallback component")
    MpvPlayerView = View
}

// MPV Player component
const MpvPlayer = forwardRef<MpvPlayerMethods, MpvPlayerProps>((props, ref) => {
    const {
        source,
        paused = false,
        volume = 1.0,
        repeat = false,
        muted = false,
        autoplay = true,
        style,
        onLoad,
        onProgress,
        onEnd,
        onError,
        onBuffer,
        onPlaying,
        onPaused,
        ...restProps
    } = props

    // Expose methods to parent component
    React.useImperativeHandle(ref, () => ({
        play: () => {
            MpvModule.play()
        },
        pause: () => {
            MpvModule.pause()
        },
        stop: () => {
            MpvModule.stop()
        },
        seek: (time: number) => {
            MpvModule.seek(time)
        },
        setVolume: (vol: number) => {
            MpvModule.setVolume(vol)
        },
        setMuted: (mute: boolean) => {
            MpvModule.setMuted(mute)
        },
    }))

    // If native module is not available, show a notification
    if (!isNativeModuleAvailable) {
        // Call onError with a notification that MPV is not supported
        React.useEffect(() => {
            if (onError) {
                onError({
                    nativeEvent: {
                        error: {
                            code: 1000,
                            message: "MPV player is not supported on this platform",
                        },
                    },
                })
            }
        }, [])

        // Return an empty view with the same dimensions
        return (
            <View
                style={[
                    {
                        overflow: "hidden",
                        backgroundColor: "black",
                        justifyContent: "center",
                        alignItems: "center",
                    },
                    style,
                ]}
            >
                <Text style={{ color: "white", textAlign: "center" }}>
                    MPV player is not supported on this platform.{"\n"}
                    Please use a native application instead.
                </Text>
            </View>
        )
    }

    // Otherwise, render the native component
    const nativeProps = {
        ...restProps,
        style: [{ overflow: "hidden" }, style],
        source: source,
        paused: paused,
        volume: volume,
        repeat: repeat,
        muted: muted,
        autoplay: autoplay,
        onMpvLoad: onLoad,
        onMpvProgress: onProgress,
        onMpvEnd: onEnd,
        onMpvError: onError,
        onMpvBuffer: onBuffer,
        onMpvPlaying: onPlaying,
        onMpvPaused: onPaused,
    }

    return <MpvPlayerView {...nativeProps} />
})

export default MpvPlayer
