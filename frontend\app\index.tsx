import { View, Text, Image, ScrollView, Pressable, Modal } from "react-native"
import "../global.css"
import { Button } from "@react-navigation/elements"
import { MediaItem } from "@/types/media"
import { useState } from "react"
import VideoPlayer from "@/components/VideoPlayer"

export default function HomeScreen() {
    const [media, setMedia] = useState<MediaItem[]>([])
    const [videoSource, setVideoSource] = useState<string>("")
    const [isVideoPlayerVisible, setIsVideoPlayerVisible] = useState<boolean>(false)
    const [selectedVideoTitle, setSelectedVideoTitle] = useState<string>("")

    const fetchMedia = async () => {
        try {
            const response = await fetch("http://localhost:7070/media")
            if (!response.ok) throw new Error("failed to fetch media")
            const data: MediaItem[] = await response.json()
            setMedia(data)
            // Just store the URL but don't automatically play
            setVideoSource(data[0].streamUrl)
        } catch (err) {
            console.error(err)
        }
    }

    const handlePlayVideo = (item: MediaItem) => {
        setVideoSource(item.streamUrl)
        setSelectedVideoTitle(item.title)
        setIsVideoPlayerVisible(true)
    }

    return (
        <View className="flex-1 items-center">
            <Button onPressOut={fetchMedia}>Connect to Plex</Button>

            {/* Video Player Modal */}
            <Modal
                visible={isVideoPlayerVisible}
                animationType="slide"
                onRequestClose={() => setIsVideoPlayerVisible(false)}
                presentationStyle="fullScreen"
            >
                <View className="flex-1 bg-black">
                    <View className="flex-row justify-between items-center p-4 bg-black">
                        <Text className="text-white text-xl font-bold">{selectedVideoTitle}</Text>
                        <Pressable onPress={() => setIsVideoPlayerVisible(false)} className="p-2">
                            <Text className="text-white text-xl">✕</Text>
                        </Pressable>
                    </View>

                    {videoSource ? (
                        <VideoPlayer videoUrl={videoSource} autoplay={true} showCustomControls={true} />
                    ) : (
                        <View className="flex-1 justify-center items-center">
                            <Text className="text-white">No video source available</Text>
                        </View>
                    )}
                </View>
            </Modal>

            <ScrollView>
                {media.map((item, index) => {
                    return (
                        <View key={index} className="flex flex-col items-center justify-center mt-10">
                            <View className="relative">
                                <Image source={{ uri: item.poster }} className="w-48 h-72 rounded"></Image>
                                <Pressable
                                    className="absolute top-[40%] left-[35%]"
                                    onPress={() => handlePlayVideo(item)}
                                >
                                    <View className="w-16 h-16 bg-white rounded-full justify-center items-center">
                                        <Image
                                            className="w-10 h-10"
                                            source={{ uri: "https://static.thenounproject.com/png/943870-200.png" }}
                                        />
                                    </View>
                                </Pressable>
                            </View>

                            <Text className="text-2xl mb-2">{item.title}</Text>
                            <Text className="max-w-[50%] text-center">{item.summary}</Text>
                        </View>
                    )
                })}
            </ScrollView>
        </View>
    )
}
