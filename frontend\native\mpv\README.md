# MPV Player Integration for React Native

This module provides a high-quality MPV player integration for React Native applications, supporting advanced formats like HDR, Dolby Vision, and TrueHD 7.1 audio across mobile and desktop platforms.

## Features

-   High-quality video playback with MPV
-   Support for advanced formats:
    -   HDR (High Dynamic Range)
    -   Dolby Vision
    -   TrueHD 7.1 audio
    -   And many more formats supported by MPV
-   Cross-platform support:
    -   Android
    -   iOS (requires additional setup)
    -   Desktop platforms via Expo for Web
-   TypeScript support
-   Consistent API across platforms

## Usage

```tsx
import React, { useRef } from "react"
import { View } from "react-native"
import MpvPlayer from "../native/mpv"
import { MpvPlayerMethods } from "../native/mpv/types"

const VideoScreen = () => {
    const playerRef = useRef<MpvPlayerMethods>(null)

    const handlePlay = () => {
        if (playerRef.current) {
            playerRef.current.play()
        }
    }

    const handlePause = () => {
        if (playerRef.current) {
            playerRef.current.pause()
        }
    }

    return (
        <View style={{ flex: 1 }}>
            <MpvPlayer
                ref={playerRef}
                source={{ uri: "https://example.com/video.mp4" }}
                style={{ width: "100%", height: "60%" }}
                paused={false}
                volume={1.0}
                repeat={false}
                muted={false}
                autoplay={true}
                onLoad={(event) => console.log("Video loaded:", event.nativeEvent)}
                onProgress={(event) => console.log("Progress:", event.nativeEvent)}
                onEnd={() => console.log("Video ended")}
                onError={(event) => console.log("Error:", event.nativeEvent)}
            />
        </View>
    )
}

export default VideoScreen
```

## Setup

### Mobile Setup

1. Run the setup script to download and configure MPV libraries:

```bash
npm run setup-mpv
```

2. For iOS, you need to install the pods:

```bash
cd ios && pod install
```

### Desktop Setup

The setup script will provide instructions for installing MPV on your desktop platform:

-   **Linux**: Install MPV using your package manager

    ```bash
    # Debian/Ubuntu
    sudo apt install mpv libmpv-dev

    # Fedora
    sudo dnf install mpv mpv-devel

    # Arch Linux
    sudo pacman -S mpv
    ```

-   **macOS**: Install MPV using Homebrew

    ```bash
    brew install mpv
    ```

-   **Windows**: The script will download and set up MPV automatically

## Running

### Mobile

```bash
# Run on Android
npm run android

# Run on iOS
npm run ios
```

### Desktop

```bash
# Run on desktop with Expo
npm run web
```

## API Reference

### Props

| Prop       | Type            | Default | Description                          |
| ---------- | --------------- | ------- | ------------------------------------ |
| source     | { uri: string } | -       | The source URL of the video          |
| paused     | boolean         | false   | Whether the video is paused          |
| volume     | number          | 1.0     | The volume of the video (0.0 to 1.0) |
| repeat     | boolean         | false   | Whether the video should loop        |
| muted      | boolean         | false   | Whether the video is muted           |
| autoplay   | boolean         | true    | Whether the video should autoplay    |
| style      | object          | -       | Style for the video container        |
| onLoad     | function        | -       | Called when the video is loaded      |
| onProgress | function        | -       | Called with progress updates         |
| onEnd      | function        | -       | Called when the video ends           |
| onError    | function        | -       | Called when an error occurs          |
| onBuffer   | function        | -       | Called when buffering state changes  |
| onPlaying  | function        | -       | Called when the video starts playing |
| onPaused   | function        | -       | Called when the video is paused      |

### Methods

| Method    | Parameters     | Description                          |
| --------- | -------------- | ------------------------------------ |
| play      | -              | Start or resume playback             |
| pause     | -              | Pause playback                       |
| stop      | -              | Stop playback                        |
| seek      | time: number   | Seek to a specific time (in seconds) |
| setVolume | volume: number | Set the volume (0.0 to 1.0)          |
| setMuted  | muted: boolean | Set whether the video is muted       |

## Advanced Configuration

For advanced use cases, you can configure MPV with additional options:

```tsx
<MpvPlayer
    source={{ uri: videoUrl }}
    // Advanced options
    config={{
        hwdec: true, // Enable hardware acceleration
        audioPassthrough: true, // Enable audio passthrough for TrueHD, etc.
        hdrMode: "auto", // HDR mode: 'auto', 'yes', or 'no'
        subtitleEnabled: true, // Enable subtitles
        subtitleFontSize: 40, // Set subtitle font size
    }}
/>
```

## Troubleshooting

### Common Issues

1. **Video doesn't play on Android**

    - Make sure you have the necessary permissions in your AndroidManifest.xml
    - Check that the MPV libraries were correctly installed

2. **HDR content doesn't display correctly**

    - Ensure your device supports HDR playback
    - Try setting `hdrMode: 'yes'` explicitly

3. **Audio passthrough not working**
    - Ensure your device and audio system support the audio format
    - Set `audioPassthrough: true` in the config
