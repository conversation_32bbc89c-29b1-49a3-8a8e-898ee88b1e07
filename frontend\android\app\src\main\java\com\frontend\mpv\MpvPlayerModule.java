package com.frontend.mpv;

import android.util.Log;
import androidx.annotation.NonNull;

import com.facebook.react.bridge.ReactApplicationContext;
import com.facebook.react.bridge.ReactContextBaseJavaModule;
import com.facebook.react.bridge.ReactMethod;
import com.facebook.react.bridge.Promise;
import com.facebook.react.module.annotations.ReactModule;

@ReactModule(name = MpvPlayerModule.NAME)
public class MpvPlayerModule extends ReactContextBaseJavaModule {
    public static final String NAME = "MpvPlayer";
    private static final String TAG = "MpvPlayerModule";

    public MpvPlayerModule(ReactApplicationContext reactContext) {
        super(reactContext);
    }

    @Override
    @NonNull
    public String getName() {
        return NAME;
    }

    @ReactMethod
    public void play() {
        Log.d(TAG, "play");
        // This will be implemented to control the MPV player instance
    }

    @ReactMethod
    public void pause() {
        Log.d(TAG, "pause");
        // This will be implemented to control the MPV player instance
    }

    @ReactMethod
    public void stop() {
        Log.d(TAG, "stop");
        // This will be implemented to control the MPV player instance
    }

    @ReactMethod
    public void seek(double time) {
        Log.d(TAG, "seek to " + time);
        // This will be implemented to control the MPV player instance
    }

    @ReactMethod
    public void setVolume(float volume) {
        Log.d(TAG, "setVolume to " + volume);
        // This will be implemented to control the MPV player instance
    }

    @ReactMethod
    public void setMuted(boolean muted) {
        Log.d(TAG, "setMuted to " + muted);
        // This will be implemented to control the MPV player instance
    }
}
