package com.frontend.mpv;

import android.content.Context;
import android.util.Log;
import android.view.SurfaceView;
import android.widget.FrameLayout;

import androidx.annotation.NonNull;

import com.facebook.react.bridge.Arguments;
import com.facebook.react.bridge.ReactContext;
import com.facebook.react.bridge.WritableMap;
import com.facebook.react.uimanager.events.RCTEventEmitter;

public class MpvPlayerView extends FrameLayout {
    private static final String TAG = "MpvPlayerView";
    
    private SurfaceView surfaceView;
    private String source;
    private boolean paused = false;
    private float volume = 1.0f;
    private boolean repeat = false;
    private boolean muted = false;
    private boolean autoplay = true;
    
    // MPV native library will be initialized here
    static {
        try {
            // Load libmpv.so
            System.loadLibrary("mpv");
            Log.d(TAG, "libmpv loaded successfully");
        } catch (UnsatisfiedLinkError e) {
            Log.e(TAG, "Failed to load libmpv: " + e.getMessage());
        }
    }

    public MpvPlayerView(@NonNull Context context) {
        super(context);
        init(context);
    }

    private void init(Context context) {
        // Create a surface view for MPV to render to
        surfaceView = new SurfaceView(context);
        addView(surfaceView, new FrameLayout.LayoutParams(
                FrameLayout.LayoutParams.MATCH_PARENT,
                FrameLayout.LayoutParams.MATCH_PARENT));
        
        // Initialize MPV here
        initializeMpv();
    }

    private void initializeMpv() {
        // This will be implemented to initialize the MPV player
        // We'll need to create JNI bindings to the libmpv C API
        Log.d(TAG, "Initializing MPV player");
    }

    public void setSource(String uri) {
        this.source = uri;
        Log.d(TAG, "Setting source: " + uri);
        
        // Load the media in MPV
        loadMedia(uri);
        
        // Notify JS that the media is loaded
        WritableMap event = Arguments.createMap();
        event.putString("uri", uri);
        sendEvent("onMpvLoad", event);
    }

    private void loadMedia(String uri) {
        // This will be implemented to load media in MPV
        Log.d(TAG, "Loading media: " + uri);
        
        // If autoplay is enabled, start playing
        if (autoplay && !paused) {
            play();
        }
    }

    public void setPaused(boolean paused) {
        this.paused = paused;
        if (paused) {
            pause();
        } else {
            play();
        }
    }

    public void setVolume(float volume) {
        this.volume = volume;
        // Set volume in MPV
        Log.d(TAG, "Setting volume: " + volume);
    }

    public void setRepeat(boolean repeat) {
        this.repeat = repeat;
        // Set repeat in MPV
        Log.d(TAG, "Setting repeat: " + repeat);
    }

    public void setMuted(boolean muted) {
        this.muted = muted;
        // Set mute in MPV
        Log.d(TAG, "Setting muted: " + muted);
    }

    public void setAutoplay(boolean autoplay) {
        this.autoplay = autoplay;
        Log.d(TAG, "Setting autoplay: " + autoplay);
    }

    public void play() {
        // Play media in MPV
        Log.d(TAG, "Playing");
        
        // Notify JS that the media is playing
        WritableMap event = Arguments.createMap();
        sendEvent("onMpvPlaying", event);
    }

    public void pause() {
        // Pause media in MPV
        Log.d(TAG, "Pausing");
        
        // Notify JS that the media is paused
        WritableMap event = Arguments.createMap();
        sendEvent("onMpvPaused", event);
    }

    public void stop() {
        // Stop media in MPV
        Log.d(TAG, "Stopping");
    }

    public void seek(double time) {
        // Seek in MPV
        Log.d(TAG, "Seeking to " + time);
    }

    private void sendEvent(String eventName, WritableMap params) {
        ReactContext reactContext = (ReactContext) getContext();
        reactContext.getJSModule(RCTEventEmitter.class).receiveEvent(
                getId(),
                eventName,
                params
        );
    }

    @Override
    protected void onDetachedFromWindow() {
        super.onDetachedFromWindow();
        // Clean up MPV resources
        Log.d(TAG, "Cleaning up MPV resources");
    }
}
