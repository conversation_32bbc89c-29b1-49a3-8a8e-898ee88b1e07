#include <jni.h>
#include <string>
#include <android/log.h>

// Include MPV headers
#include <mpv/client.h>
#include <mpv/render_gl.h>

#define TAG "MpvJNI"
#define LOGI(...) __android_log_print(ANDROID_LOG_INFO, TAG, __VA_ARGS__)
#define LOGE(...) __android_log_print(ANDROID_LOG_ERROR, TAG, __VA_ARGS__)

// MPV handle
static mpv_handle *mpv = nullptr;

extern "C" {

// Initialize MPV
JNIEXPORT jlong JNICALL
Java_com_frontend_mpv_MpvPlayerView_nativeInit(JNIEnv *env, jobject thiz) {
    LOGI("Initializing MPV");
    
    // Create MPV instance
    mpv = mpv_create();
    if (!mpv) {
        LOGE("Failed to create MPV instance");
        return 0;
    }
    
    // Configure MPV
    mpv_set_option_string(mpv, "vo", "gpu");
    mpv_set_option_string(mpv, "gpu-api", "opengl");
    
    // Initialize MPV
    if (mpv_initialize(mpv) < 0) {
        LOGE("Failed to initialize MPV");
        mpv_destroy(mpv);
        mpv = nullptr;
        return 0;
    }
    
    LOGI("MPV initialized successfully");
    return (jlong)(intptr_t)mpv;
}

// Load media
JNIEXPORT void JNICALL
Java_com_frontend_mpv_MpvPlayerView_nativeLoadMedia(JNIEnv *env, jobject thiz, jlong handle, jstring uri) {
    if (!handle) {
        LOGE("Invalid MPV handle");
        return;
    }
    
    mpv_handle *mpv = (mpv_handle *)(intptr_t)handle;
    const char *uri_str = env->GetStringUTFChars(uri, nullptr);
    
    LOGI("Loading media: %s", uri_str);
    
    const char *cmd[] = {"loadfile", uri_str, nullptr};
    mpv_command(mpv, cmd);
    
    env->ReleaseStringUTFChars(uri, uri_str);
}

// Play
JNIEXPORT void JNICALL
Java_com_frontend_mpv_MpvPlayerView_nativePlay(JNIEnv *env, jobject thiz, jlong handle) {
    if (!handle) {
        LOGE("Invalid MPV handle");
        return;
    }
    
    mpv_handle *mpv = (mpv_handle *)(intptr_t)handle;
    
    LOGI("Playing");
    
    int flag = 0;
    mpv_set_property(mpv, "pause", MPV_FORMAT_FLAG, &flag);
}

// Pause
JNIEXPORT void JNICALL
Java_com_frontend_mpv_MpvPlayerView_nativePause(JNIEnv *env, jobject thiz, jlong handle) {
    if (!handle) {
        LOGE("Invalid MPV handle");
        return;
    }
    
    mpv_handle *mpv = (mpv_handle *)(intptr_t)handle;
    
    LOGI("Pausing");
    
    int flag = 1;
    mpv_set_property(mpv, "pause", MPV_FORMAT_FLAG, &flag);
}

// Stop
JNIEXPORT void JNICALL
Java_com_frontend_mpv_MpvPlayerView_nativeStop(JNIEnv *env, jobject thiz, jlong handle) {
    if (!handle) {
        LOGE("Invalid MPV handle");
        return;
    }
    
    mpv_handle *mpv = (mpv_handle *)(intptr_t)handle;
    
    LOGI("Stopping");
    
    const char *cmd[] = {"stop", nullptr};
    mpv_command(mpv, cmd);
}

// Seek
JNIEXPORT void JNICALL
Java_com_frontend_mpv_MpvPlayerView_nativeSeek(JNIEnv *env, jobject thiz, jlong handle, jdouble time) {
    if (!handle) {
        LOGE("Invalid MPV handle");
        return;
    }
    
    mpv_handle *mpv = (mpv_handle *)(intptr_t)handle;
    
    LOGI("Seeking to %f", time);
    
    char seek_str[32];
    snprintf(seek_str, sizeof(seek_str), "%.2f", time);
    
    const char *cmd[] = {"seek", seek_str, "absolute", nullptr};
    mpv_command(mpv, cmd);
}

// Set volume
JNIEXPORT void JNICALL
Java_com_frontend_mpv_MpvPlayerView_nativeSetVolume(JNIEnv *env, jobject thiz, jlong handle, jfloat volume) {
    if (!handle) {
        LOGE("Invalid MPV handle");
        return;
    }
    
    mpv_handle *mpv = (mpv_handle *)(intptr_t)handle;
    
    LOGI("Setting volume to %f", volume);
    
    double vol = volume * 100.0; // MPV volume is 0-100
    mpv_set_property(mpv, "volume", MPV_FORMAT_DOUBLE, &vol);
}

// Set mute
JNIEXPORT void JNICALL
Java_com_frontend_mpv_MpvPlayerView_nativeSetMute(JNIEnv *env, jobject thiz, jlong handle, jboolean mute) {
    if (!handle) {
        LOGE("Invalid MPV handle");
        return;
    }
    
    mpv_handle *mpv = (mpv_handle *)(intptr_t)handle;
    
    LOGI("Setting mute to %d", mute);
    
    int flag = mute ? 1 : 0;
    mpv_set_property(mpv, "mute", MPV_FORMAT_FLAG, &flag);
}

// Destroy MPV
JNIEXPORT void JNICALL
Java_com_frontend_mpv_MpvPlayerView_nativeDestroy(JNIEnv *env, jobject thiz, jlong handle) {
    if (!handle) {
        LOGE("Invalid MPV handle");
        return;
    }
    
    mpv_handle *mpv = (mpv_handle *)(intptr_t)handle;
    
    LOGI("Destroying MPV");
    
    mpv_destroy(mpv);
}

} // extern "C"
