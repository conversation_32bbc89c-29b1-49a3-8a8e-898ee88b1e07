// MPV Player TypeScript interfaces

// Source object for the video
export interface MpvSource {
    uri: string
}

// Event data interfaces
export interface MpvLoadEvent {
    duration: number
    width?: number
    height?: number
    videoCodec?: string
    audioCodec?: string
}

export interface MpvProgressEvent {
    currentTime: number
    bufferedTime?: number
}

export interface MpvErrorEvent {
    error: {
        code?: number
        message: string
    }
}

export interface MpvBufferEvent {
    isBuffering: boolean
}

// Props for the MPV player component
export interface MpvPlayerProps {
    // Video source
    source: MpvSource

    // Playback controls
    paused?: boolean
    volume?: number
    repeat?: boolean
    muted?: boolean
    autoplay?: boolean

    // Styling
    style?: any

    // Event callbacks
    onLoad?: (event: { nativeEvent: MpvLoadEvent }) => void
    onProgress?: (event: { nativeEvent: MpvProgressEvent }) => void
    onEnd?: (event: { nativeEvent: {} }) => void
    onError?: (event: { nativeEvent: MpvErrorEvent }) => void
    onBuffer?: (event: { nativeEvent: MpvBufferEvent }) => void
    onPlaying?: (event: { nativeEvent: {} }) => void
    onPaused?: (event: { nativeEvent: {} }) => void
}

// Methods that should be exposed by the player
export interface MpvPlayerMethods {
    play: () => void
    pause: () => void
    stop: () => void
    seek: (time: number) => void
    setVolume: (volume: number) => void
    setMuted: (muted: boolean) => void
}

// Additional configuration options for MPV
export interface MpvPlayerConfig {
    // Hardware acceleration
    hwdec?: boolean
    // Advanced audio options
    audioPassthrough?: boolean
    // HDR options
    hdrMode?: "auto" | "yes" | "no"
    // Subtitle options
    subtitleEnabled?: boolean
    subtitleFontSize?: number
}
