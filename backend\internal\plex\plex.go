package plex

import (
	"encoding/json"
	"fmt"
	"io"
	"net/http"
)

type PlexClient struct {
	BaseURL          string
	ClientIdentifier string
	Token            string
	Client           *http.Client
}

type RoundTripper struct {
	BaseURL    string
	AdminToken string
}

func NewPlexClient(baseURL, adminToken string) *PlexClient {
	return &PlexClient{
		BaseURL:          baseURL,
		Token:            adminToken,
		Client: &http.Client{},
	}
}

type PlexResponse struct {
	MediaContainer struct {
		Metadata []Metadata `json:"Metadata"`
	} `json:"MediaContainer"`
}

type Metadata struct {
	Title   string `json:"title"`
	Summary string `json:"summary"`
	Media   []Media `json:"Media"`
	Image   []Image `json:"Image"`
	Thumb   string  `json:"thumb"`
}

type Media struct {
	Part []Part `json:"Part"`
}

type Part struct {
	Key string `json:"key"` // Used to construct the stream URL
}

type Image struct {
	Type string `json:"type"` // e.g. "coverPoster"
	URL  string `json:"url"`  // Path to the poster
}


func (c *PlexClient) Get(endpoint string) ([]byte, error) {
	url := c.BaseURL + endpoint

	req, err := http.NewRequest("GET", url, nil)
	if err != nil {
		return nil, fmt.Errorf("failed to create request: %w", err)
	}

	req.Header.Set("X-Plex-Token", c.Token)
	req.Header.Set("Accept", "application/json")

	resp, err := c.Client.Do(req)
	if err != nil {
		return nil, fmt.Errorf("failed to get from url %s: %w", url, err)
	}

	defer resp.Body.Close()

	if resp.StatusCode != http.StatusOK {
		return nil, fmt.Errorf("API returned an error: %s", resp.Status)
	}

	body, err := io.ReadAll(resp.Body)
	if err != nil {
		return nil, fmt.Errorf("failed to read response body: %w", err)
	}

	return body, nil
}


func getJSON(responseBody io.ReadCloser, result any) error {
	body, err := io.ReadAll(responseBody)
	if err != nil {
		return fmt.Errorf("failed to read response body: %w", err)
	}

	if err := json.Unmarshal(body, result); err != nil {
		return fmt.Errorf("failed to parse JSON: %w", err)
	}

	return nil
}

func (c *PlexClient) FetchSpongebob() (*PlexResponse, error) {
	body, err := c.Get("/library/metadata/21682/")
	if err != nil {
		fmt.Printf("Error fetching spongebob: %v\n", err)
		return nil, err
	}

	var response PlexResponse
	if err := json.Unmarshal(body, &response); err != nil {
		fmt.Printf("Error unmarshaling spongebob into plex repsonse: %w\n", err)
		return nil, err
	}

	return &response, nil


}