// VideoPlayer.tsx
import React, { useRef, useState, useEffect } from "react"
import { View, Text, Pressable, ActivityIndicator } from "react-native"
// Import our custom MPV player
import MpvPlayer from "../native/mpv"
import { MpvPlayerMethods } from "../native/mpv/types"
// For NativeWind, ensure your components can accept className
// For custom components or complex ones, you might need `styled` from `nativewind`
// import { styled } from 'nativewind';
// const StyledView = styled(View);
// const StyledText = styled(Text);

interface VideoPlayerProps {
    videoUrl: string // videoUrl is now mandatory
    autoplay?: boolean
    showCustomControls?: boolean
}

const VideoPlayer: React.FC<VideoPlayerProps> = ({ videoUrl, autoplay = false, showCustomControls = true }) => {
    const playerRef = useRef<MpvPlayerMethods>(null)
    const [paused, setPaused] = useState<boolean>(!autoplay)
    const [error, setError] = useState<string | null>(null)
    const [isLoading, setIsLoading] = useState<boolean>(true)
    const [isBuffering, setIsBuffering] = useState<boolean>(false)
    const [duration, setDuration] = useState<number>(0)
    const [currentTime, setCurrentTime] = useState<number>(0)

    useEffect(() => {
        // If videoUrl changes, reset player state
        setIsLoading(true)
        setError(null)
        setPaused(!autoplay)
        setCurrentTime(0)
        setDuration(0)
    }, [videoUrl, autoplay])

    const onBuffer = (event: any) => {
        console.log("Buffering:", event.isBuffering)
        setIsBuffering(event.isBuffering)
    }

    const onError = (event: any) => {
        console.error("Video Error:", event)
        let errorMessage = "An unknown video error occurred."

        // Handle different error event formats
        if (event.nativeEvent?.error) {
            // Format from MPV player
            errorMessage = event.nativeEvent.error.message || "Unknown error"
        } else if (event.error) {
            // Alternative format
            errorMessage = event.error.message || "Unknown error"
        }

        // If the error is related to the MPV not being supported,
        // we can show a more specific error message
        if (errorMessage.includes("MPV player is not supported")) {
            errorMessage = "MPV player is not supported on this platform. Please run the app as a native application."
        }

        setError(errorMessage)
        setIsLoading(false)
    }

    // This function is kept for reference but not used with MPV player
    // as the MPV player handles load start internally
    /*
    const onLoadStart = () => {
        console.log("Video load started for URL:", videoUrl)
        setIsLoading(true)
        setError(null)
    }
    */

    const onLoad = (event: any) => {
        console.log("Video loaded successfully: ", event)

        // Handle different event formats
        let duration = 0
        if (event.nativeEvent) {
            // Format from MPV player
            duration = event.nativeEvent.duration || 0
        } else {
            // Alternative format
            duration = event.duration || 0
        }

        setDuration(duration)
        setIsLoading(false)
        if (autoplay) {
            setPaused(false)
        }
    }

    const onProgress = (event: any) => {
        // Handle different event formats
        let currentTime = 0
        if (event.nativeEvent) {
            // Format from MPV player
            currentTime = event.nativeEvent.currentTime || 0
        } else {
            // Alternative format
            currentTime = event.currentTime || 0
        }

        setCurrentTime(currentTime)
    }

    const onEnd = () => {
        console.log("Video finished playing")
        setPaused(true)
        if (playerRef.current) {
            playerRef.current.seek(0)
        }
    }

    const onPlaying = () => {
        console.log("Video is playing")
        setPaused(false)
    }

    const onPaused = () => {
        console.log("Video is paused")
        setPaused(true)
    }

    const togglePlayPause = () => {
        if (paused) {
            if (playerRef.current) playerRef.current.play()
            setPaused(false)
        } else {
            if (playerRef.current) playerRef.current.pause()
            setPaused(true)
        }
    }

    const handleSeek = (time: number) => {
        if (playerRef.current) {
            playerRef.current.seek(time)
            setCurrentTime(time)
        }
    }

    // Simple function to format time (MM:SS)
    const formatTime = (seconds: number): string => {
        const mins = Math.floor(seconds / 60)
        const secs = Math.floor(seconds % 60)
        return `${mins < 10 ? "0" : ""}${mins}:${secs < 10 ? "0" : ""}${secs}`
    }

    if (!videoUrl) {
        return (
            <View className="flex-1 justify-center items-center bg-black">
                <Text className="text-red-500 text-lg">Error: No video URL provided.</Text>
            </View>
        )
    }

    return (
        <View className="flex-1 bg-black">
            <MpvPlayer
                ref={playerRef}
                source={{ uri: videoUrl }}
                style={{ width: "100%", height: "60%", backgroundColor: "black" }}
                paused={paused}
                volume={1.0}
                repeat={false}
                muted={false}
                autoplay={autoplay}
                onLoad={onLoad}
                onProgress={onProgress}
                onEnd={onEnd}
                onError={onError}
                onBuffer={onBuffer}
                onPlaying={onPlaying}
                onPaused={onPaused}
            />

            {(isLoading || (isBuffering && !error)) && (
                <View className="absolute top-0 left-0 right-0 bottom-0 justify-center items-center bg-black/50">
                    <ActivityIndicator size="large" color="#FFFFFF" />
                    <Text className="text-white mt-2">{isLoading ? "Loading..." : "Buffering..."}</Text>
                </View>
            )}

            {error && (
                <View className="absolute top-1/2 left-0 right-0 p-4 items-center">
                    <Text className="text-red-500 text-center bg-black/70 p-2 rounded">Error: {error}</Text>
                </View>
            )}

            {showCustomControls && !error && !isLoading && (
                <View className="p-4 absolute bottom-0 left-0 right-0 bg-black/70">
                    {/* Progress Bar - Basic Example */}
                    <View className="h-2 bg-gray-600 rounded-full mb-2">
                        <View
                            style={{ width: duration > 0 ? `${(currentTime / duration) * 100}%` : "0%" }}
                            className="h-full bg-red-500 rounded-full"
                        />
                    </View>
                    <View className="flex-row justify-between items-center mb-2">
                        <Text className="text-white text-xs">{formatTime(currentTime)}</Text>
                        <Text className="text-white text-xs">{formatTime(duration)}</Text>
                    </View>

                    <View className="flex-row justify-around items-center">
                        {/* Example: Rewind Button */}
                        <Pressable onPress={() => handleSeek(Math.max(0, currentTime - 10))} className="p-2">
                            <Text className="text-white text-lg">RW</Text>
                        </Pressable>

                        <Pressable
                            onPress={togglePlayPause}
                            className="bg-red-500 rounded-full w-12 h-12 justify-center items-center"
                        >
                            <Text className="text-white text-xl">{paused ? "▶" : "❚❚"}</Text>
                        </Pressable>

                        {/* Example: Fast Forward Button */}
                        <Pressable onPress={() => handleSeek(Math.min(duration, currentTime + 10))} className="p-2">
                            <Text className="text-white text-lg">FF</Text>
                        </Pressable>
                    </View>
                </View>
            )}
        </View>
    )
}

export default VideoPlayer
